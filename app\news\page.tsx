'use client';

import { useState, useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { AnimatedText } from '@/components/ui/animated-text';
import { BokehBackground } from '@/components/ui/aceternity/bokeh-background';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import { ArrowRight, Calendar, Search, Filter, TrendingUp, Clock, Newspaper } from 'lucide-react';
import TextHoverEffectDemo from '@/components/ui/text-hover-effect-demo';
import { useLanguage } from '@/lib/context/language-context';
import Footer from '@/components/shared/Footer';

// Define the type for news articles
interface NewsArticle {
  id: number;
  title: string;
  excerpt: string;
  date: string;
  category: string;
  image: string;
  slug: string;
  featured?: boolean;
  readTime?: string;
}

const categories = ['All', 'Program', 'Success Story', 'Company News', 'Events', 'Partnership'];

// Enhanced NewsCard component with modern design
const NewsCard = ({ article, index }: { article: NewsArticle; index: number }) => {
  const isLarge = index === 0;
  const isTall = index === 1;

  return (
    <motion.div
      className="group relative h-full overflow-hidden rounded-2xl bg-gradient-to-br from-black/40 via-black/20 to-purple-950/30 backdrop-blur-xl border border-white/10 hover:border-primary/30 transition-all duration-500"
      whileHover={{ y: -5, scale: 1.02 }}
      transition={{ duration: 0.3 }}
    >
      <Link href={`/news/${article.slug}`} className="h-full block">
        <div className="relative h-full flex flex-col">
          {/* Header image */}
          <div className={`relative overflow-hidden ${isLarge ? 'aspect-[21/9]' : isTall ? 'aspect-[16/12]' : 'aspect-[16/9]'}`}>
            <Image
              src={article.image}
              alt={article.title}
              fill
              className="object-cover transition-all duration-700 group-hover:scale-110 group-hover:brightness-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />

            {/* Category badge */}
            <div className="absolute top-6 left-6">
              <motion.span
                className="inline-flex items-center rounded-full border px-4 py-2 text-sm font-medium border-primary/40 bg-primary/30 text-white backdrop-blur-md shadow-lg"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 + 0.2 }}
              >
                {article.category}
              </motion.span>
            </div>

            {/* Featured badge */}
            {article.featured && (
              <div className="absolute top-6 right-6">
                <motion.div
                  className="inline-flex items-center rounded-full border px-4 py-2 text-sm font-medium border-yellow-400/40 bg-yellow-400/30 text-yellow-300 backdrop-blur-md shadow-lg"
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: index * 0.1 + 0.4 }}
                >
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Featured
                </motion.div>
              </div>
            )}

            {/* Gradient overlay on hover */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-t from-primary/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              initial={{ opacity: 0 }}
            />
          </div>

          {/* Content */}
          <div className="flex-grow p-8">
            <div className="flex items-center gap-4 mb-4 text-sm text-white/60">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2" />
                {article.date}
              </div>
              {article.readTime && (
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-2" />
                  {article.readTime}
                </div>
              )}
            </div>

            <h3 className={`font-bold mb-4 group-hover:text-primary transition-colors duration-300 line-clamp-2 ${isLarge ? 'text-2xl' : 'text-xl'}`}>
              {article.title}
            </h3>

            <p className={`text-white/70 leading-relaxed line-clamp-3 ${isLarge ? 'text-base' : 'text-sm'}`}>
              {article.excerpt}
            </p>

            {/* Read More Button */}
            <div className="mt-6">
              <motion.div
                className="inline-flex items-center text-primary hover:text-primary/80 transition-colors duration-300 group/btn"
                whileHover={{ x: 5 }}
              >
                <span className="font-medium">Read More</span>
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
              </motion.div>
            </div>
          </div>
        </div>
      </Link>
    </motion.div>
  );
};
export default function NewsPage() {
  const { t } = useLanguage();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('All');
  const containerRef = useRef<HTMLDivElement>(null);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  const opacity = useTransform(scrollYProgress, [0, 0.3, 0.7, 1], [0, 1, 1, 0]);
  const scale = useTransform(scrollYProgress, [0, 0.3, 0.7, 1], [0.8, 1, 1, 0.8]);

  const newsArticles: NewsArticle[] = [
    {
      id: 1,
      title: t('news.article1.title'),
      excerpt: t('news.article1.excerpt'),
      date: 'May 15, 2024',
      category: 'Finance',
      image: '/images/programs/1.jpg',
      slug: 'market-analysis',
      featured: true,
      readTime: '5 min read',
    },
    {
      id: 2,
      title: t('news.article2.title'),
      excerpt: t('news.article2.excerpt'),
      date: 'May 10, 2024',
      category: 'Finance',
      image: '/images/programs/2.jpg',
      slug: 'mongolia-finance-analysis',
      readTime: '3 min read',
    },
    {
      id: 3,
      title: t('news.article3.title'),
      excerpt: t('news.article3.excerpt'),
      date: 'May 5, 2024',
      category: 'Data',
      image: '/images/programs/plant-bulbs.jpg',
      slug: 'data-processing-solongo',
      readTime: '7 min read',
    },
    {
      id: 4,
      title: t('news.article4.title'),
      excerpt: t('news.article4.excerpt'),
      date: 'April 28, 2024',
      category: 'International',
      image: '/images/programs/1.jpg',
      slug: 'international-market-entry',
      readTime: '4 min read',
    },
  ];

  const featuredArticle = {
    id: 5,
    title: t('news.featured.title'),
    excerpt: t('news.featured.excerpt'),
    date: 'June 5, 2024',
    category: 'Marketing',
    image: '/images/programs/2.jpg',
    slug: 'product-marketing-vs-marketing',
    featured: true,
    readTime: '6 min read',
  };

  // Filter articles based on search and category
  const filteredArticles = newsArticles.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         article.excerpt.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = activeCategory === 'All' || article.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="min-h-screen bg-black">
      {/* Background Text Effect - Footer Area Only */}
      <div className="fixed bottom-0 left-0 right-0 h-[600px] z-[-10] flex items-center justify-center pointer-events-none">
        <div className="pointer-events-auto">
          <TextHoverEffectDemo />
        </div>
      </div>

      {/* Hero Section */}
      <BokehBackground
        className="min-h-[70vh] flex flex-col items-center justify-center pt-32 pb-20 relative overflow-hidden"
        colors={['#8b5cf6', '#a855f7', '#c084fc', '#e879f9', '#fbbf24', '#f59e0b']}
        density={80}
        speed={1.5}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-black/50 via-black/30 to-black/70 z-0" />

        <motion.div
          ref={containerRef}
          style={{ opacity, scale }}
          className="container mx-auto px-4 relative z-10"
        >
          <div className="text-center max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="inline-block rounded-full bg-primary/20 backdrop-blur-sm border border-primary/30 px-6 py-3 text-sm font-medium text-primary mb-8"
            >
              <Newspaper className="inline-block w-4 h-4 mr-2" />
              News & Insights
            </motion.div>

            <AnimatedText
              text="Innovation Stories & Updates"
              className="text-4xl md:text-6xl font-bold tracking-tight mb-6 bg-gradient-to-r from-white via-white to-primary/80 bg-clip-text text-transparent"
            />

            <AnimatedText
              text="Stay updated with our latest announcements, success stories, and upcoming events that shape the future of innovation."
              className="text-xl text-white/70 leading-relaxed max-w-4xl mx-auto"
              once
            />
          </div>
        </motion.div>
      </BokehBackground>

      {/* Search and Filter Section */}
      <section className="py-16 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-black via-purple-950/20 to-black" />
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[length:50px_50px]" />

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            className="mb-16 max-w-6xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="flex flex-col md:flex-row gap-6 items-center justify-between bg-black/40 backdrop-blur-xl p-8 rounded-2xl border border-primary/20 shadow-2xl">
              <div className="relative w-full md:w-auto flex-grow">
                <Search className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-primary/60" />
                <Input
                  type="text"
                  placeholder="Search articles..."
                  className="pl-12 h-12 bg-black/30 border-primary/30 text-white placeholder:text-white/50 rounded-xl focus:border-primary/50 focus:ring-primary/20"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex items-center gap-3 w-full md:w-auto overflow-x-auto pb-2 md:pb-0">
                <Filter className="h-5 w-5 text-primary hidden md:block" />
                <div className="flex gap-3">
                  {categories.map((category) => (
                    <Button
                      key={category}
                      variant={activeCategory === category ? "default" : "outline"}
                      onClick={() => setActiveCategory(category)}
                      size="sm"
                      className={`whitespace-nowrap transition-all duration-300 h-10 px-4 rounded-xl ${
                        activeCategory === category
                          ? 'bg-primary/30 text-white border-primary/50 hover:bg-primary/40 shadow-lg shadow-primary/20'
                          : 'bg-black/30 border-white/20 text-white/80 hover:bg-primary/10 hover:border-primary/30 hover:text-white'
                      }`}
                    >
                      {category}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>

          {/* News Grid - Creative Layout */}
          <motion.div
            className="max-w-7xl mx-auto mb-20"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 auto-rows-fr">
              {filteredArticles.map((article, index) => (
                <motion.div
                  key={article.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className={`${index === 0 ? 'md:col-span-2 lg:col-span-2' : ''} ${index === 1 ? 'lg:row-span-2' : ''}`}
                >
                  <NewsCard article={article} index={index} />
                </motion.div>
              ))}
            </div>
          </motion.div>

        </div>
      </section>

      {/* Featured Article Section */}
      <BokehBackground
        className="py-20 relative overflow-hidden"
        colors={['#8b5cf6', '#a855f7', '#c084fc', '#e879f9']}
        density={40}
        speed={1}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-black/60 via-black/40 to-black/60 z-0" />

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            className="max-w-7xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="text-center mb-12">
              <motion.h2
                className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-white to-primary/80 bg-clip-text text-transparent"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
              >
                Featured Article
              </motion.h2>
              <motion.p
                className="text-xl text-white/70 max-w-2xl mx-auto"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                viewport={{ once: true }}
              >
                Don&apos;t miss our most important story of the week
              </motion.p>
            </div>

            <motion.div
              className="bg-gradient-to-br from-black/40 via-black/20 to-purple-950/30 backdrop-blur-xl border border-white/10 rounded-3xl overflow-hidden hover:border-primary/30 transition-all duration-500"
              whileHover={{ y: -10, scale: 1.02 }}
              transition={{ duration: 0.3 }}
            >
              <Link href={`/news/${featuredArticle.slug}`}>
                <div className="flex flex-col lg:flex-row">
                  <div className="lg:w-1/2 relative aspect-[16/9] lg:aspect-auto">
                    <Image
                      src={featuredArticle.image}
                      alt={featuredArticle.title}
                      fill
                      className="object-cover transition-all duration-700 hover:scale-105 hover:brightness-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent to-black/20 lg:to-black/60" />
                  </div>
                  <div className="lg:w-1/2 p-8 lg:p-12">
                    <div className="flex items-center gap-4 mb-6">
                      <span className="inline-flex items-center rounded-full border px-4 py-2 text-sm font-medium border-primary/40 bg-primary/30 text-white backdrop-blur-md">
                        {featuredArticle.category}
                      </span>
                      <div className="flex items-center text-sm text-white/60">
                        <Calendar className="h-4 w-4 mr-2" />
                        {featuredArticle.date}
                      </div>
                      <div className="flex items-center text-sm text-white/60">
                        <Clock className="h-4 w-4 mr-2" />
                        {featuredArticle.readTime}
                      </div>
                    </div>

                    <h3 className="text-3xl lg:text-4xl font-bold text-white mb-6 hover:text-primary transition-colors duration-300 leading-tight">
                      {featuredArticle.title}
                    </h3>

                    <p className="text-white/70 text-lg leading-relaxed mb-8">
                      {featuredArticle.excerpt}
                    </p>

                    <motion.div
                      className="inline-flex items-center bg-primary/20 hover:bg-primary/30 text-white px-6 py-3 rounded-xl transition-all duration-300 group border border-primary/30"
                      whileHover={{ x: 5 }}
                    >
                      <span className="font-medium">Read Full Article</span>
                      <ArrowRight className="ml-3 h-5 w-5 transition-transform group-hover:translate-x-1" />
                    </motion.div>
                  </div>
                </div>
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </BokehBackground>

      {/* Footer */}
      <Footer />
    </div>
  );
}
