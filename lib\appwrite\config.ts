import { Client, Account, Databases, Storage, Teams } from 'appwrite';

// Appwrite configuration
export const appwriteConfig = {
  url: process.env.NEXT_PUBLIC_APPWRITE_URL || 'https://cloud.appwrite.io/v1',
  projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || '',
  databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID || '',
  storageId: process.env.NEXT_PUBLIC_APPWRITE_STORAGE_ID || '',
  
  // Collection IDs
  usersCollectionId: process.env.NEXT_PUBLIC_APPWRITE_USERS_COLLECTION_ID || '',
  coursesCollectionId: process.env.NEXT_PUBLIC_APPWRITE_COURSES_COLLECTION_ID || '',
  lessonsCollectionId: process.env.NEXT_PUBLIC_APPWRITE_LESSONS_COLLECTION_ID || '',
  enrollmentsCollectionId: process.env.NEXT_PUBLIC_APPWRITE_ENROLLMENTS_COLLECTION_ID || '',
  blogPostsCollectionId: process.env.NEXT_PUBLIC_APPWRITE_BLOG_POSTS_COLLECTION_ID || '',
  progressCollectionId: process.env.NEXT_PUBLIC_APPWRITE_PROGRESS_COLLECTION_ID || '',
  couponsCollectionId: process.env.NEXT_PUBLIC_APPWRITE_COUPONS_COLLECTION_ID || '',
};

// Initialize Appwrite client
const client = new Client();

client
  .setEndpoint(appwriteConfig.url)
  .setProject(appwriteConfig.projectId);

// Initialize services
export const account = new Account(client);
export const databases = new Databases(client);
export const storage = new Storage(client);
export const teams = new Teams(client);

export default client;
