import nodemailer from 'nodemailer';
import { render } from '@react-email/render';

export interface EmailTemplate {
  subject: string;
  html: string;
  text?: string;
}

export interface EmailRecipient {
  email: string;
  name?: string;
}

export interface EmailData {
  to: EmailRecipient;
  template: EmailTemplate;
  from?: {
    email: string;
    name: string;
  };
}

class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });
  }

  /**
   * Send email
   */
  async sendEmail(data: EmailData): Promise<boolean> {
    try {
      const { to, template, from } = data;
      
      const mailOptions = {
        from: from ? `"${from.name}" <${from.email}>` : `"InnoHub" <${process.env.SMTP_USER}>`,
        to: to.name ? `"${to.name}" <${to.email}>` : to.email,
        subject: template.subject,
        html: template.html,
        text: template.text || this.htmlToText(template.html),
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Email sent successfully:', result.messageId);
      return true;
    } catch (error) {
      console.error('Failed to send email:', error);
      return false;
    }
  }

  /**
   * Send welcome email to new users
   */
  async sendWelcomeEmail(user: { email: string; name: string }): Promise<boolean> {
    const template = this.generateWelcomeTemplate(user);
    return this.sendEmail({
      to: { email: user.email, name: user.name },
      template
    });
  }

  /**
   * Send course enrollment confirmation
   */
  async sendEnrollmentConfirmation(
    user: { email: string; name: string },
    course: { title: string; instructor: string; id: string }
  ): Promise<boolean> {
    const template = this.generateEnrollmentTemplate(user, course);
    return this.sendEmail({
      to: { email: user.email, name: user.name },
      template
    });
  }

  /**
   * Send course completion certificate
   */
  async sendCertificate(
    user: { email: string; name: string },
    course: { title: string; instructor: string },
    certificateNumber: string
  ): Promise<boolean> {
    const template = this.generateCertificateTemplate(user, course, certificateNumber);
    return this.sendEmail({
      to: { email: user.email, name: user.name },
      template
    });
  }

  /**
   * Send progress reminder
   */
  async sendProgressReminder(
    user: { email: string; name: string },
    course: { title: string; progress: number }
  ): Promise<boolean> {
    const template = this.generateProgressReminderTemplate(user, course);
    return this.sendEmail({
      to: { email: user.email, name: user.name },
      template
    });
  }

  /**
   * Send coupon notification
   */
  async sendCouponNotification(
    user: { email: string; name: string },
    coupon: { code: string; description: string; discountValue: number; expirationDate?: Date }
  ): Promise<boolean> {
    const template = this.generateCouponTemplate(user, coupon);
    return this.sendEmail({
      to: { email: user.email, name: user.name },
      template
    });
  }

  /**
   * Send payment confirmation
   */
  async sendPaymentConfirmation(
    user: { email: string; name: string },
    payment: { 
      amount: number; 
      currency: string; 
      course: { title: string }; 
      transactionId: string;
    }
  ): Promise<boolean> {
    const template = this.generatePaymentConfirmationTemplate(user, payment);
    return this.sendEmail({
      to: { email: user.email, name: user.name },
      template
    });
  }

  /**
   * Generate welcome email template
   */
  private generateWelcomeTemplate(user: { name: string }): EmailTemplate {
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Welcome to InnoHub</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #8B5CF6, #3B82F6); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }
            .button { display: inline-block; background: #8B5CF6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Welcome to InnoHub! 🎉</h1>
              <p>Танд тавтай морил, ${user.name}!</p>
            </div>
            <div class="content">
              <h2>Your Learning Journey Starts Here</h2>
              <p>Thank you for joining InnoHub, Mongolia's premier online learning platform for entrepreneurs and innovators.</p>
              
              <h3>What's Next?</h3>
              <ul>
                <li>🎯 Explore our comprehensive course catalog</li>
                <li>📚 Start with our free introductory courses</li>
                <li>🎁 Use coupon code <strong>WELCOME2024</strong> for free access to any course</li>
                <li>🏆 Track your progress and earn certificates</li>
              </ul>

              <a href="${process.env.NEXTAUTH_URL}/courses/catalog" class="button">
                Start Learning Now
              </a>

              <p>If you have any questions, our support team is here to <NAME_EMAIL></p>
            </div>
            <div class="footer">
              <p>© 2024 InnoHub. All rights reserved.</p>
              <p>Ulaanbaatar, Mongolia</p>
            </div>
          </div>
        </body>
      </html>
    `;

    return {
      subject: 'Welcome to InnoHub - Start Your Learning Journey! 🚀',
      html,
      text: `Welcome to InnoHub, ${user.name}! Your learning journey starts here. Use coupon code WELCOME2024 for free access to any course. Visit ${process.env.NEXTAUTH_URL}/courses/catalog to get started.`
    };
  }

  /**
   * Generate enrollment confirmation template
   */
  private generateEnrollmentTemplate(
    user: { name: string },
    course: { title: string; instructor: string; id: string }
  ): EmailTemplate {
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Course Enrollment Confirmation</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #10B981, #8B5CF6); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }
            .course-card { background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #8B5CF6; margin: 20px 0; }
            .button { display: inline-block; background: #8B5CF6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Enrollment Confirmed! ✅</h1>
              <p>Амжилттай бүртгэгдлээ!</p>
            </div>
            <div class="content">
              <h2>Hello ${user.name},</h2>
              <p>Congratulations! You have successfully enrolled in:</p>
              
              <div class="course-card">
                <h3>${course.title}</h3>
                <p><strong>Instructor:</strong> ${course.instructor}</p>
                <p>You now have full access to all course materials, videos, and assessments.</p>
              </div>

              <h3>Next Steps:</h3>
              <ul>
                <li>📖 Start with the first lesson</li>
                <li>📝 Complete assessments to track your progress</li>
                <li>🏆 Earn your certificate upon completion</li>
                <li>💬 Join course discussions and connect with peers</li>
              </ul>

              <a href="${process.env.NEXTAUTH_URL}/courses/${course.id}" class="button">
                Start Learning
              </a>

              <p>Happy learning!</p>
            </div>
            <div class="footer">
              <p>© 2024 InnoHub. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `;

    return {
      subject: `Enrollment Confirmed: ${course.title} 🎓`,
      html,
      text: `Hello ${user.name}, you have successfully enrolled in "${course.title}" by ${course.instructor}. Start learning at ${process.env.NEXTAUTH_URL}/courses/${course.id}`
    };
  }

  /**
   * Generate certificate email template
   */
  private generateCertificateTemplate(
    user: { name: string },
    course: { title: string; instructor: string },
    certificateNumber: string
  ): EmailTemplate {
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Certificate of Completion</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #F59E0B, #8B5CF6); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }
            .certificate { background: white; padding: 30px; border-radius: 8px; border: 2px solid #F59E0B; margin: 20px 0; text-align: center; }
            .button { display: inline-block; background: #F59E0B; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🏆 Congratulations!</h1>
              <p>Баяр хүргэе!</p>
            </div>
            <div class="content">
              <h2>Certificate Earned!</h2>
              <p>Dear ${user.name},</p>
              <p>Congratulations on successfully completing the course!</p>
              
              <div class="certificate">
                <h3>🎓 Certificate of Completion</h3>
                <h4>${course.title}</h4>
                <p><strong>Instructor:</strong> ${course.instructor}</p>
                <p><strong>Certificate Number:</strong> ${certificateNumber}</p>
                <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
              </div>

              <p>Your certificate is now available in your profile and can be shared on LinkedIn or other professional networks.</p>

              <a href="${process.env.NEXTAUTH_URL}/courses/profile" class="button">
                View Certificate
              </a>

              <p>Keep learning and growing with InnoHub!</p>
            </div>
            <div class="footer">
              <p>© 2024 InnoHub. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `;

    return {
      subject: `🏆 Certificate Earned: ${course.title}`,
      html,
      text: `Congratulations ${user.name}! You have earned a certificate for completing "${course.title}". Certificate Number: ${certificateNumber}. View it at ${process.env.NEXTAUTH_URL}/courses/profile`
    };
  }

  /**
   * Generate progress reminder template
   */
  private generateProgressReminderTemplate(
    user: { name: string },
    course: { title: string; progress: number }
  ): EmailTemplate {
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Continue Your Learning Journey</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #3B82F6, #8B5CF6); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }
            .progress-bar { background: #e5e7eb; height: 20px; border-radius: 10px; margin: 20px 0; }
            .progress-fill { background: #8B5CF6; height: 100%; border-radius: 10px; width: ${course.progress}%; }
            .button { display: inline-block; background: #8B5CF6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Continue Learning! 📚</h1>
              <p>Сурахаа үргэлжлүүлээрэй!</p>
            </div>
            <div class="content">
              <h2>Hi ${user.name},</h2>
              <p>You're making great progress in <strong>${course.title}</strong>!</p>
              
              <div class="progress-bar">
                <div class="progress-fill"></div>
              </div>
              <p style="text-align: center;"><strong>${course.progress}% Complete</strong></p>

              <p>Don't lose momentum! Continue where you left off and complete your learning journey.</p>

              <a href="${process.env.NEXTAUTH_URL}/courses/dashboard" class="button">
                Continue Learning
              </a>

              <p>Remember: Consistency is key to mastering new skills!</p>
            </div>
            <div class="footer">
              <p>© 2024 InnoHub. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `;

    return {
      subject: `Continue Learning: ${course.title} (${course.progress}% complete)`,
      html,
      text: `Hi ${user.name}, you're ${course.progress}% through "${course.title}". Continue learning at ${process.env.NEXTAUTH_URL}/courses/dashboard`
    };
  }

  /**
   * Generate coupon notification template
   */
  private generateCouponTemplate(
    user: { name: string },
    coupon: { code: string; description: string; discountValue: number; expirationDate?: Date }
  ): EmailTemplate {
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Special Offer Just for You!</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #EF4444, #F59E0B); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }
            .coupon { background: white; padding: 30px; border-radius: 8px; border: 2px dashed #F59E0B; margin: 20px 0; text-align: center; }
            .coupon-code { font-size: 24px; font-weight: bold; color: #F59E0B; background: #FEF3C7; padding: 10px 20px; border-radius: 6px; display: inline-block; margin: 10px 0; }
            .button { display: inline-block; background: #F59E0B; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🎁 Special Offer!</h1>
              <p>Онцгой санал!</p>
            </div>
            <div class="content">
              <h2>Hi ${user.name},</h2>
              <p>We have a special offer just for you!</p>
              
              <div class="coupon">
                <h3>🏷️ ${coupon.description}</h3>
                <div class="coupon-code">${coupon.code}</div>
                <p><strong>${coupon.discountValue}% OFF</strong></p>
                ${coupon.expirationDate ? `<p>Valid until: ${coupon.expirationDate.toLocaleDateString()}</p>` : ''}
              </div>

              <p>Use this coupon code at checkout to get your discount on any course!</p>

              <a href="${process.env.NEXTAUTH_URL}/courses/catalog" class="button">
                Browse Courses
              </a>

              <p>Don't miss out on this limited-time offer!</p>
            </div>
            <div class="footer">
              <p>© 2024 InnoHub. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `;

    return {
      subject: `🎁 Special Offer: ${coupon.discountValue}% OFF with code ${coupon.code}`,
      html,
      text: `Hi ${user.name}, use coupon code ${coupon.code} for ${coupon.discountValue}% off any course! ${coupon.description}. Browse courses at ${process.env.NEXTAUTH_URL}/courses/catalog`
    };
  }

  /**
   * Generate payment confirmation template
   */
  private generatePaymentConfirmationTemplate(
    user: { name: string },
    payment: { 
      amount: number; 
      currency: string; 
      course: { title: string }; 
      transactionId: string;
    }
  ): EmailTemplate {
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Payment Confirmation</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #10B981, #3B82F6); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }
            .receipt { background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #10B981; margin: 20px 0; }
            .button { display: inline-block; background: #10B981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Payment Confirmed! ✅</h1>
              <p>Төлбөр амжилттай!</p>
            </div>
            <div class="content">
              <h2>Thank you, ${user.name}!</h2>
              <p>Your payment has been successfully processed.</p>
              
              <div class="receipt">
                <h3>Payment Receipt</h3>
                <p><strong>Course:</strong> ${payment.course.title}</p>
                <p><strong>Amount:</strong> ${payment.amount} ${payment.currency}</p>
                <p><strong>Transaction ID:</strong> ${payment.transactionId}</p>
                <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
              </div>

              <p>You now have full access to the course. Start learning immediately!</p>

              <a href="${process.env.NEXTAUTH_URL}/courses/dashboard" class="button">
                Access Your Course
              </a>

              <p>If you have any questions about your purchase, please contact our support team.</p>
            </div>
            <div class="footer">
              <p>© 2024 InnoHub. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `;

    return {
      subject: `Payment Confirmed: ${payment.course.title} - ${payment.amount} ${payment.currency}`,
      html,
      text: `Thank you ${user.name}! Your payment of ${payment.amount} ${payment.currency} for "${payment.course.title}" has been confirmed. Transaction ID: ${payment.transactionId}. Access your course at ${process.env.NEXTAUTH_URL}/courses/dashboard`
    };
  }

  /**
   * Convert HTML to plain text (basic implementation)
   */
  private htmlToText(html: string): string {
    return html
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .trim();
  }
}

export const emailService = new EmailService();
export default emailService;
